<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web3Forms Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea, select { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007cba; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; }
        button:disabled { background: #ccc; }
        .debug { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; font-family: monospace; }
    </style>
</head>
<body>
    <h1>Web3Forms Test Form</h1>
    <p>This is a simple test to debug the Web3Forms integration.</p>
    
    <div id="debug-info" class="debug">
        <strong>Debug Information:</strong><br>
        <span id="debug-text">Ready to test...</span>
    </div>

    <form id="testForm" action="https://api.web3forms.com/submit" method="POST">
        <!-- Web3Forms API Key -->
        <input type="hidden" name="access_key" value="3281a1c3-7f45-4915-9368-b24a72cbb920">
        
        <!-- Recipient Email -->
        <input type="hidden" name="to" value="<EMAIL>">
        
        <!-- Subject -->
        <input type="hidden" name="subject" value="Test Form Submission">
        
        <!-- From Name -->
        <input type="hidden" name="from_name" value="Furniture Mall Test Form">
        
        <!-- Honeypot for spam protection -->
        <input type="checkbox" name="botcheck" style="display: none;">

        <div class="form-group">
            <label for="name">Name:</label>
            <input type="text" id="name" name="name" required>
        </div>

        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" required>
        </div>

        <div class="form-group">
            <label for="message">Message:</label>
            <textarea id="message" name="message" rows="4" required></textarea>
        </div>

        <button type="submit" id="submitBtn">Send Test Message</button>
    </form>

    <script>
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const debugDiv = document.getElementById('debug-text');
            const submitBtn = document.getElementById('submitBtn');
            const originalText = submitBtn.textContent;
            
            // Show loading state
            submitBtn.textContent = 'Sending...';
            submitBtn.disabled = true;
            debugDiv.innerHTML = 'Preparing form data...';
            
            try {
                const formData = new FormData(this);
                
                // Debug: Show form data
                let formDataText = 'Form data being sent:<br>';
                for (let [key, value] of formData.entries()) {
                    formDataText += `${key}: ${value}<br>`;
                }
                debugDiv.innerHTML = formDataText + '<br>Sending request...';
                
                const response = await fetch('https://api.web3forms.com/submit', {
                    method: 'POST',
                    body: formData
                });
                
                debugDiv.innerHTML = formDataText + `<br>Response status: ${response.status}<br>Parsing response...`;
                
                const data = await response.json();
                
                debugDiv.innerHTML = formDataText + `<br>Response status: ${response.status}<br>Response data: ${JSON.stringify(data, null, 2)}`;
                
                if (data.success) {
                    alert('✅ SUCCESS! Form submitted successfully.');
                    this.reset();
                } else {
                    alert(`❌ ERROR: ${data.message || 'Unknown error occurred'}`);
                }
            } catch (error) {
                debugDiv.innerHTML = `❌ NETWORK ERROR: ${error.message}<br>Check your internet connection and API key.`;
                alert(`Network Error: ${error.message}`);
            } finally {
                // Reset button state
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        });
    </script>
</body>
</html>
