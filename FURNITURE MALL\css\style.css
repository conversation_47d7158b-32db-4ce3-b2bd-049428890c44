/* Global Styles */
:root {
    --primary-color: #03ACFF;
    --secondary-color: #151426;
    --accent-color: #1e3893;
    --text-color: #1b1b1b;
    --light-text: #fff;
    --light-bg: #f8f9fa;
    --dark-bg: #1a1a1a;
    --border-color: #e1e1e1;
    --font-primary: 'Poppins', sans-serif;
    --font-secondary: 'Playfair Display', serif;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    color: var(--text-color);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;

}

a {
    text-decoration: none;
    color: inherit;
    transition: var(--transition);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 15px;
}

h1 {
    font-size: 3.5rem;
    font-family: var(--font-secondary);
}

h2 {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 30px;
    position: relative;
    font-family: var(--font-secondary);
}

h2:after {
    content: '';
    display: block;
    width: 80px;
    height: 3px;
    background-color: var(--accent-color);
    margin: 15px auto 0;
}

p {
    margin-bottom: 20px;
}

.section-description {
    text-align: center;
    max-width: 700px;
    margin: 0 auto 40px;
    color: var(--text-color);
}

/* Buttons */
.btn-primary {
    display: inline-block;
    background-color: var(--primary-color);
    color: var(--light-text);
    padding: 12px 30px;
    border-radius: 4px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: var(--transition);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    transform: translateY(-3px);
}

.btn-secondary {
    display: inline-block;
    background-color: transparent;
    color: var(--primary-color);
    padding: 10px 25px;
    border: 2px solid var(--primary-color);
    border-radius: 4px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: var(--transition);
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: var(--light-text);
    transform: translateY(-3px);
}

.btn-tertiary {
    display: inline-block;
    background-color: var(--primary-color);
    color: var(--light-text);
    padding: 8px 20px;
    border-radius: 4px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: var(--transition);
}

.btn-tertiary:hover {
    background-color: var(--secondary-color);
    color: var(--light-text);
}

/* Header */
header {
    background-color: transparent;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    transition: var(--transition);
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
}

.logo-container {
    flex: 0 0 220px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo {
    max-height: 100px;
    object-fit: contain;
    border-radius: 4px;
    transition: var(--transition);
}

.nav-links {
    display: flex;
    gap: 30px;
}

.nav-links a {
    font-weight: 500;
    position: relative;
    color: var(--light-text);
}

.nav-links a:hover,
.nav-links a.active {
    color: var(--accent-color);
}

.nav-links a.active:after,
.nav-links a:hover:after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--accent-color);
}

.mobile-menu {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--light-text);
}

/* Hero Section */
.hero {
    position: relative;
    color: var(--light-text);
    height: 100vh;
    /* Full viewport height */
    text-align: center;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.slideshow-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: -1;
}

.slideshow-slides {
    position: relative;
    width: 100%;
    height: 100%;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.slide.active {
    opacity: 1;
}

.slide img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.slideshow-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6));
}

.slideshow-controls {
    position: absolute;
    bottom: 20px;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    gap: 10px;
    z-index: 10;
}

.slide-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: var(--transition);
}

.slide-dot.active {
    background-color: var(--accent-color);
    transform: scale(1.2);
}

.hero-content {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
    z-index: 1;
    padding: 0 20px;
}

.hero h1 {
    margin-bottom: 20px;
    font-size: 4rem;
    text-transform: uppercase;
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

/* Featured Promo Section - Image Slideshow */
.featured-promo {
    position: relative;
    width: 100%;
    height: 350px;
    overflow: hidden;
    margin: 30px 0;
}

.featured-promo .slideshow-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.featured-promo .slideshow-slides {
    position: relative;
    width: 100%;
    height: 100%;
}

.featured-promo .slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

.featured-promo .slide.active {
    opacity: 1;
}

.featured-promo .slide img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    object-position: center;
    display: block;
    background-color: #f8f9fa;
}

/* Responsive promo images - Desktop shows PC version, Mobile shows mobile version */
.featured-promo .slide .mobile-promo-img {
    display: none;
}

.featured-promo .slide .desktop-promo-img {
    display: block;
}

/* Mobile view - show mobile image, hide desktop image */
@media (max-width: 768px) {
    .featured-promo .slide .mobile-promo-img {
        display: block;
    }

    .featured-promo .slide .desktop-promo-img {
        display: none;
    }
}

.featured-promo .slideshow-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: var(--primary-color);
    color: var(--light-text);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    transition: all 0.3s ease;
}

.featured-promo .slideshow-nav:hover {
    background: var(--primary-color);
    transform: translateY(-50%) scale(1.1);
}

.featured-promo .prev-slide {
    left: 0;
}

.featured-promo .next-slide {
    right: 0;
}

.featured-promo .slideshow-controls {
    position: absolute;
    bottom: 25px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 12px;
    z-index: 10;
}

.featured-promo .slide-dot {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.6);
    border: 2px solid rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.3s ease;
}

.featured-promo .slide-dot:hover {
    background-color: rgba(255, 255, 255, 0.8);
    transform: scale(1.1);
}

.featured-promo .slide-dot.active {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    transform: scale(1.2);
}

/* New Arrivals Section */
.new-arrivals {
    padding: 80px 0;
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.product-card {
    background-color: var(--light-text);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: var(--transition);
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.product-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-tag {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: var(--accent-color);
    color: var(--primary-color);
    padding: 5px 10px;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.8rem;
}

.product-info {
    padding: 20px;
    text-align: center;
}

.product-info h3 {
    font-size: 1.2rem;
    margin-bottom: 10px;
}

.product-description {
    color: var(--text-color);
    font-size: 0.9rem;
    margin-bottom: 15px;
}

.view-more {
    text-align: center;
}

/* Banner Section */
.banner {
    position: relative;
    color: var(--light-text);
    padding: 100px 0;
    text-align: center;
    overflow: hidden;
}

.banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6));
    z-index: 1;
}

.banner img.banner-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 0;
}

.banner-content {
    position: relative;
    max-width: 600px;
    margin: 0 auto;
    z-index: 2;
}

.banner h2 {
    font-size: 3rem;
    margin-bottom: 20px;
}

.banner h2:after {
    background-color: var(--accent-color);
}

.banner p {
    font-size: 1.2rem;
    margin-bottom: 30px;
}

/* Featured Collections */
.featured-collections {
    padding: 80px 0;
}

.collection-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 220px);
    gap: 15px;
}

/* Catalogue Page Styles */
.page-banner {
    position: relative;
    height: 300px;
    color: var(--light-text);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin-bottom: 40px;
}

.page-banner .banner-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -1;
}

.page-banner .banner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6));
    z-index: 0;
}

.page-banner .banner-content {
    position: relative;
    z-index: 1;
    text-align: center;
    max-width: 800px;
    padding: 0 20px;
}

.page-banner h1 {
    font-size: 3rem;
    margin-bottom: 15px;
}

.page-banner p {
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

.catalogue-filters {
    padding: 20px 0 40px;
}

.filter-container {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 20px;
    background-color: var(--light-bg);
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-group label {
    font-weight: 600;
    color: var(--text-color);
}

.filter-select {
    padding: 8px 15px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--light-text);
    font-family: var(--font-primary);
    cursor: pointer;
    min-width: 150px;
}

.catalogue-section {
    padding: 40px 0;
    border-bottom: 1px solid var(--border-color);
}

.catalogue-section:last-of-type {
    border-bottom: none;
}

.product-price {
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.collection-card {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    cursor: pointer;
}

.collection-card.large {
    grid-column: span 2;
}

.collection-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.collection-card:hover img {
    transform: scale(1.1);
}

.collection-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 20px;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: var(--light-text);
    transition: var(--transition);
    text-align: center;
}

.collection-overlay h3 {
    margin-bottom: 10px;
}

/* Testimonials Section */
.testimonials {
    padding: 80px 0;
    background-color: var(--light-bg);
}

.testimonials {
    padding: 80px 0;
    background-color: var(--light-bg);
}

.testimonial-slider {
    position: relative;
    max-width: 800px;
    margin: 0 auto 30px;
    overflow: hidden;
    padding: 0 10px;
    /* Add padding for the navigation buttons */
}

.testimonial-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    pointer-events: none;
}

.testimonial-slide.active {
    opacity: 1;
    position: relative;
    pointer-events: auto;
}

.testimonial-content {
    background-color: var(--light-text);
    padding: 40px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    text-align: center;
    position: relative;
    z-index: 1;
}

/* Testimonial Navigation Buttons - Desktop */
.testimonial-slider .testimonial-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: var(--primary-color);
    color: var(--light-text);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    transition: all 0.3s ease;
}

.testimonial-slider .testimonial-nav:hover {
    background: var(--primary-color);
    transform: translateY(-50%) scale(1.1);
}

.testimonial-slider .prev-testimonial {
    left: 0;
}

.testimonial-slider .next-testimonial {
    right: 0;
}

.testimonial-text {
    font-size: 1.1rem;
    font-style: italic;
    margin-bottom: 20px;
    line-height: 1.8;
    color: var(--text-color);
}

.testimonial-author {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.testimonial-dots {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.testimonial-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: var(--border-color);
    cursor: pointer;
    transition: var(--transition);
}

.testimonial-dot.active {
    background-color: var(--primary-color);
    transform: scale(1.2);
}

/* Our Brands Section */
.our-brands {
    padding: 80px 0;
    text-align: center;
    background-color: var(--light-text);
}

.our-brands h2 {
    margin-bottom: 50px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.brands-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.brand-card {
    border: 1px solid var(--border-color);
    padding: 30px 20px;
    border-radius: 4px;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    height: 100%;
}

.brand-card:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.brand-logo {
    margin-bottom: 20px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.brand-logo img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.brand-description {
    font-size: 0.9rem;
    line-height: 1.6;
    color: var(--text-color);
    text-align: center;
}

/* Newsletter Section */
.newsletter {
    background-color: var(--primary-color);
    color: var(--light-text);
    padding: 60px 0;
    text-align: center;
}

.newsletter h2:after {
    background-color: var(--accent-color);
}

.newsletter p {
    max-width: 600px;
    margin: 0 auto 30px;
}

.newsletter-form {
    display: flex;
    max-width: 500px;
    margin: 0 auto;
}

.newsletter-form input {
    flex: 1;
    padding: 12px 15px;
    border: none;
    border-radius: 4px 0 0 4px;
    font-size: 1rem;
}

.newsletter-form button {
    border: none;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
}

/* Footer */
footer {
    background-color: var(--dark-bg);
    color: var(--light-text);
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 30px;
    margin-bottom: 40px;
}

.footer-logo img {
    max-height: 60px;
    margin-bottom: 15px;
}

.footer-logo p {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.footer-logo .tagline {
    font-size: 0.9rem;
    color: var(--border-color);
}

.footer-links h3,
.footer-contact h3 {
    font-size: 1.2rem;
    margin-bottom: 20px;
    position: relative;
}

.footer-links h3:after,
.footer-contact h3:after {
    content: '';
    display: block;
    width: 40px;
    height: 2px;
    background-color: var(--accent-color);
    margin-top: 10px;
}

.footer-links ul li {
    margin-bottom: 10px;
}

.footer-links a {
    color: var(--border-color);
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--accent-color);
    padding-left: 5px;
}

.footer-contact p {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    color: var(--border-color);
}

.footer-contact .branch-info {
    margin-top: 15px;
    margin-bottom: 5px;
    color: var(--accent-color);
}

.footer-contact i {
    color: var(--accent-color);
}

.footer-contact .social-icons {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.footer-contact .social-icons a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transition: var(--transition);
}

.footer-contact .social-icons a:hover {
    background-color: var(--accent-color);
    color: var(--primary-color);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.copyright {
    color: var(--border-color);
    margin-bottom: 0;
}

.payment-methods img {
    height: 30px;
}

/* Responsive Styles */
@media (max-width: 1024px) {
    h1 {
        font-size: 3rem;
    }

    h2 {
        font-size: 2.2rem;
    }

    .footer-content {
        grid-template-columns: repeat(3, 1fr);
    }

    .footer-logo {
        grid-column: span 3;
        text-align: center;
    }

    .footer-logo img {
        margin: 0 auto 15px;
    }
}

@media (max-width: 768px) {
    header .container {
        padding: 10px 20px;
    }

    .logo-container {
        flex: 0 0 150px;
    }

    .logo {
        max-height: 60px;
    }

    /* We'll handle nav-links display in mobile.css */
    .nav-icons {
        display: none;
    }

    .mobile-menu {
        display: block;
    }

    .hero {
        padding: 100px 0;
    }

    .hero h1 {
        font-size: 3rem;
    }

    .collection-grid {
        grid-template-columns: 1fr;
    }

    .collection-card.large {
        grid-column: auto;
    }

    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-logo {
        grid-column: span 2;
    }

    .newsletter-form {
        flex-direction: column;
    }

    .newsletter-form input {
        border-radius: 4px;
        margin-bottom: 10px;
    }

    .newsletter-form button {
        border-radius: 4px;
    }
}

@media (max-width: 480px) {
    h1 {
        font-size: 2.5rem;
    }

    h2 {
        font-size: 1.8rem;
    }

    .hero p {
        font-size: 1rem;
    }

    .category-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-content {
        grid-template-columns: 1fr;
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }
}

/* About Page Styles */
.page-header {
    background-color: var(--primary-color);
    color: var(--light-text);
    padding: 60px 0;
    text-align: center;
}

.page-header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.breadcrumb {
    font-size: 0.9rem;
}

.breadcrumb a {
    color: var(--light-text);
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.about-intro {
    padding: 80px 0;
}

.about-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    align-items: center;
}

.about-content h1 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: var(--primary-color);
}

.about-content h2 {
    margin-bottom: 30px;
    position: relative;
}

.about-content h2:after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background-color: var(--accent-color);
    margin-top: 15px;
}

.about-content p {
    margin-bottom: 20px;
    line-height: 1.8;
}

.about-image img {
    width: 100%;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Why Choose Us Section */
.why-choose-us {
    padding: 80px 0;
    background-color: var(--light-bg);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.feature-card {
    background-color: var(--light-text);
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    text-align: center;
    transition: var(--transition);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    font-size: 2.5rem;
    color: var(--accent-color);
    margin-bottom: 20px;
}

.feature-card h3 {
    margin-bottom: 15px;
}

/* Showroom Section */
.our-showroom {
    padding: 80px 0;
}

.showroom-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    align-items: center;
}

.image-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 15px;
}

.image-grid img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 8px;
    transition: var(--transition);
}

.image-grid img:hover {
    transform: scale(1.05);
}

.showroom-content h2 {
    margin-bottom: 30px;
    position: relative;
}

.showroom-content h2:after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background-color: var(--accent-color);
    margin-top: 15px;
}

.showroom-content p {
    margin-bottom: 20px;
    line-height: 1.8;
}

.showroom-content .btn-primary {
    margin-top: 10px;
}

/* Our Commitment Section */
.our-commitment {
    padding: 80px 0;
    background-color: var(--light-bg);
}

.commitment-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.commitment-card {
    background-color: var(--light-text);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: var(--transition);
}

.commitment-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.commitment-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.commitment-card h3 {
    padding: 20px 20px 10px;
    margin-bottom: 0;
}

.commitment-card p {
    padding: 0 20px 20px;
}

/* Our Brands Section */
.our-brands {
    padding: 80px 0;
    text-align: center;
    background-color: var(--light-text);
}

.our-brands h2 {
    margin-bottom: 50px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.brands-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.brand-card {
    border: 1px solid var(--border-color);
    padding: 30px 20px;
    border-radius: 4px;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    height: 100%;
}

.brand-card:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.brand-logo {
    margin-bottom: 20px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.brand-logo img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.brand-description {
    font-size: 0.9rem;
    line-height: 1.6;
    color: var(--text-color);
    text-align: center;
}

/* CTA Section */
.cta {
    padding: 100px 0;
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('../images/cta-bg.jpg');
    background-size: cover;
    background-position: center;
    color: var(--light-text);
    text-align: center;
}

.cta h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.cta p {
    font-size: 1.2rem;
    max-width: 700px;
    margin: 0 auto 30px;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    color: var(--light-text);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 999;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.back-to-top.active {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background-color: var(--accent-color);
    transform: translateY(-5px);
}

/* Product Details Modal */
.product-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    overflow-y: auto;
}

.modal-content {
    background-color: white;
    margin: 2% auto;
    border-radius: 12px;
    width: 90%;
    max-width: 900px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--light-bg);
    border-radius: 12px 12px 0 0;
}

.modal-header h2 {
    margin: 0;
    color: var(--primary-color);
    font-size: 1.8rem;
}

.modal-close {
    font-size: 2rem;
    font-weight: bold;
    color: var(--text-color);
    cursor: pointer;
    transition: var(--transition);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.modal-close:hover {
    background-color: var(--primary-color);
    color: var(--light-text);
}

.modal-body {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    padding: 30px;
}

.modal-image img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.modal-details {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.product-category {
    background-color: var(--secondary-color);
    color: var(--light-text);
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    align-self: flex-start;
}

.product-price {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--primary-color);
    margin: 10px 0;
}

.product-description {
    color: var(--text-color);
    line-height: 1.6;
    font-size: 1.1rem;
}

.product-features h3,
.product-specifications h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1.2rem;
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 8px;
}

.product-features ul {
    list-style: none;
    padding: 0;
}

.product-features li {
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);
    position: relative;
    padding-left: 25px;
}

.product-features li:before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--secondary-color);
    font-weight: bold;
}

.product-features li:last-child {
    border-bottom: none;
}

.spec-item {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid var(--border-color);
}

.spec-item:last-child {
    border-bottom: none;
}

.spec-item strong {
    color: var(--primary-color);
    min-width: 120px;
}

.modal-actions {
    display: flex;
    gap: 15px;
    margin-top: 20px;
    padding: 20px 30px;
    border-top: 1px solid var(--border-color);
    background-color: var(--light-bg);
    border-radius: 0 0 12px 12px;
}

.modal-actions .btn-primary,
.modal-actions .btn-secondary {
    flex: 1;
    text-align: center;
    padding: 12px 20px;
    border-radius: 6px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: var(--transition);
}

/* Responsive Styles for About Page */
@media (max-width: 992px) {

    .about-grid,
    .showroom-grid {
        grid-template-columns: 1fr;
    }

    .about-image,
    .showroom-images {
        order: -1;
    }

    .brands-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {

    .features-grid,
    .commitment-grid {
        grid-template-columns: 1fr;
    }

    .image-grid {
        grid-template-columns: 1fr;
        grid-template-rows: repeat(4, 1fr);
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .brands-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .brand-card {
        padding: 20px 15px;
    }

    .brand-logo {
        height: 70px;
        margin-bottom: 15px;
    }
}

/* Catalogue Page Styles */
.catalogue-page header {
    position: relative;
    background-color: var(--primary-color);
}

/* Catalogue Layout */
.catalogue-layout {
    display: flex;
    max-width: 1200px;
    margin: 0 auto;
    gap: 30px;
    padding: 0 20px;
}

/* Catalogue Sidebar */
.catalogue-sidebar {
    width: 280px;
    flex-shrink: 0;
    background: var(--light-bg);
    border-radius: 8px;
    padding: 25px;
    height: fit-content;
    position: sticky;
    top: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.catalogue-sidebar h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 1.3rem;
    font-weight: 600;
}

.category-list {
    list-style: none;
    margin-bottom: 30px;
}

.category-list li {
    margin-bottom: 8px;
}

.category-link {
    display: block;
    padding: 12px 15px;
    color: var(--text-color);
    text-decoration: none;
    border-radius: 6px;
    transition: var(--transition);
    font-weight: 500;
}

.category-link:hover,
.category-link.active {
    background-color: var(--primary-color);
    color: var(--light-text);
    transform: translateX(5px);
}

.sidebar-contact {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    color: var(--light-text);
}

.sidebar-contact h4 {
    margin-bottom: 8px;
    font-size: 1.1rem;
}

.sidebar-contact p {
    margin-bottom: 15px;
    font-size: 0.9rem;
    opacity: 0.9;
}

.whatsapp-sidebar-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: #25D366;
    color: white;
    padding: 10px 15px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: var(--transition);
}

.whatsapp-sidebar-btn:hover {
    background: #128C7E;
    transform: scale(1.05);
}

/* Main Content Area */
.catalogue-main {
    flex: 1;
    min-width: 0;
}

/* Mobile Sidebar Toggle */
.mobile-sidebar-toggle {
    display: none;
}

.mobile-sidebar-btn {
    background: var(--primary-color);
    color: var(--light-text);
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
}

.mobile-sidebar-btn:hover {
    background: var(--accent-color);
}

/* WhatsApp Floating Button */
.whatsapp-float {
    position: fixed;
    bottom: 25px;
    left: 25px;
    z-index: 1000;
}

.whatsapp-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: #25D366;
    color: white;
    border-radius: 50%;
    text-decoration: none;
    font-size: 1.8rem;
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.4);
    transition: var(--transition);
    animation: pulse 2s infinite;
}

.whatsapp-btn:hover {
    background: #128C7E;
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(37, 211, 102, 0.6);
}

@keyframes pulse {
    0% {
        box-shadow: 0 4px 15px rgba(37, 211, 102, 0.4);
    }

    50% {
        box-shadow: 0 4px 25px rgba(37, 211, 102, 0.7);
    }

    100% {
        box-shadow: 0 4px 15px rgba(37, 211, 102, 0.4);
    }
}

.page-banner {
    position: relative;
    height: 300px;
    color: var(--light-text);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin-bottom: 40px;
}

.page-banner .banner-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -1;
}

.page-banner .banner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6));
    z-index: 0;
}

.page-banner .banner-content {
    position: relative;
    z-index: 1;
    text-align: center;
    max-width: 800px;
    padding: 0 20px;
}

.page-banner h1 {
    font-size: 3rem;
    margin-bottom: 15px;
}

.page-banner p {
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

.catalogue-filters {
    padding: 20px 0 40px;
}

.filter-container {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 20px;
    background-color: var(--light-bg);
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-group label {
    font-weight: 600;
    color: var(--text-color);
}

.filter-select {
    padding: 8px 15px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--light-text);
    font-family: var(--font-primary);
    cursor: pointer;
    min-width: 150px;
}

.catalogue-section {
    padding: 40px 0;
    border-bottom: 1px solid var(--border-color);
}

.catalogue-section:last-of-type {
    border-bottom: none;
}

.product-price {
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 15px;
    font-size: 1.1rem;
}

/* Privacy Policy Page Styles */
.privacy-page header {
    position: relative;
    background-color: var(--primary-color);
}

.privacy-content {
    padding: 80px 0;
}

.privacy-policy {
    max-width: 900px;
    margin: 0 auto;
    background-color: var(--light-text);
    padding: 40px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.policy-header {
    text-align: center;
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: 2px solid var(--light-bg);
}

.policy-header h2 {
    color: var(--primary-color);
    font-size: 2rem;
    margin-bottom: 10px;
}

.policy-header h3 {
    color: var(--accent-color);
    font-size: 1.5rem;
    margin-bottom: 20px;
}

.policy-section {
    margin-bottom: 35px;
}

.policy-section h3 {
    color: var(--primary-color);
    font-size: 1.3rem;
    margin-bottom: 15px;
    position: relative;
}

.policy-section h3:after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background-color: var(--accent-color);
    margin-top: 8px;
}

.policy-section p {
    line-height: 1.8;
    margin-bottom: 15px;
    color: var(--text-color);
}

.policy-section ul {
    margin-left: 20px;
    margin-bottom: 15px;
}

.policy-section li {
    line-height: 1.8;
    margin-bottom: 8px;
    color: var(--text-color);
}

.contact-details {
    background-color: var(--light-bg);
    padding: 20px;
    border-radius: 8px;
    margin-top: 15px;
}

.contact-details p {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.contact-details i {
    color: var(--primary-color);
    width: 20px;
}

.policy-footer {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 2px solid var(--light-bg);
    text-align: center;
}

.policy-footer p {
    margin-bottom: 15px;
}

.last-updated {
    color: var(--text-color);
    font-style: italic;
    font-size: 0.9rem;
}

/* Customer Care Pages Styles */
.faq-page header,
.delivery-page header,
.warranty-page header,
.terms-page header {
    position: relative;
    background-color: var(--primary-color);
}

/* FAQ Page Styles */
.faq-content {
    padding: 80px 0;
}

.faq-sections {
    max-width: 900px;
    margin: 0 auto;
}

.faq-section {
    margin-bottom: 50px;
    background-color: var(--light-text);
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.faq-section h2 {
    color: var(--primary-color);
    font-size: 1.8rem;
    margin-bottom: 25px;
    position: relative;
}

.faq-section h2:after {
    content: '';
    display: block;
    width: 60px;
    height: 3px;
    background-color: var(--accent-color);
    margin-top: 10px;
}

.faq-item {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--light-bg);
}

.faq-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.faq-item h3 {
    color: var(--primary-color);
    font-size: 1.2rem;
    margin-bottom: 10px;
}

.faq-item p {
    line-height: 1.8;
    color: var(--text-color);
}

/* Delivery Information Page Styles */
.delivery-content {
    padding: 80px 0;
}

.delivery-info {
    max-width: 900px;
    margin: 0 auto;
}

.info-section {
    margin-bottom: 50px;
    background-color: var(--light-text);
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.info-section h2 {
    color: var(--primary-color);
    font-size: 1.8rem;
    margin-bottom: 25px;
    position: relative;
}

.info-section h2:after {
    content: '';
    display: block;
    width: 60px;
    height: 3px;
    background-color: var(--accent-color);
    margin-top: 10px;
}

.info-item {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--light-bg);
}

.info-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.info-item h3 {
    color: var(--primary-color);
    font-size: 1.2rem;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.info-item h3 i {
    color: var(--accent-color);
    font-size: 1.1rem;
}

.info-item p {
    line-height: 1.8;
    color: var(--text-color);
}

/* Warranty Page Styles */
.warranty-content {
    padding: 80px 0;
}

.warranty-info {
    max-width: 900px;
    margin: 0 auto;
}

.warranty-section {
    margin-bottom: 50px;
    background-color: var(--light-text);
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.warranty-section h2 {
    color: var(--primary-color);
    font-size: 1.8rem;
    margin-bottom: 25px;
    position: relative;
}

.warranty-section h2:after {
    content: '';
    display: block;
    width: 60px;
    height: 3px;
    background-color: var(--accent-color);
    margin-top: 10px;
}

.warranty-item {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--light-bg);
}

.warranty-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.warranty-item h3 {
    color: var(--primary-color);
    font-size: 1.2rem;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.warranty-item h3 i {
    color: var(--accent-color);
    font-size: 1.1rem;
}

.warranty-item p {
    line-height: 1.8;
    color: var(--text-color);
}

.claim-steps {
    display: grid;
    gap: 20px;
}

.step {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 20px;
    background-color: var(--light-bg);
    border-radius: 8px;
}

.step-number {
    background-color: var(--primary-color);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    flex-shrink: 0;
}

.step-content h4 {
    color: var(--primary-color);
    margin-bottom: 8px;
}

.step-content p {
    line-height: 1.6;
    color: var(--text-color);
}

/* Terms & Conditions Page Styles */
.terms-content {
    padding: 80px 0;
}

.terms-document {
    max-width: 900px;
    margin: 0 auto;
    background-color: var(--light-text);
    padding: 40px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.terms-section {
    margin-bottom: 35px;
}

.terms-section h2 {
    color: var(--primary-color);
    font-size: 1.3rem;
    margin-bottom: 15px;
    position: relative;
}

.terms-section h2:after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background-color: var(--accent-color);
    margin-top: 8px;
}

.terms-section ul {
    margin-left: 20px;
    margin-bottom: 15px;
}

.terms-section li {
    line-height: 1.8;
    margin-bottom: 8px;
    color: var(--text-color);
}

.terms-footer {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 2px solid var(--light-bg);
    text-align: center;
}

.terms-footer p {
    margin-bottom: 15px;
}

.last-updated {
    color: var(--text-color);
    font-style: italic;
    font-size: 0.9rem;
}

/* Contact Section for Customer Care Pages */
.contact-section {
    background-color: var(--light-bg);
    padding: 30px;
    border-radius: 8px;
    margin-top: 30px;
    text-align: center;
}

.contact-section h2 {
    color: var(--primary-color);
    margin-bottom: 15px;
}

.contact-section p {
    margin-bottom: 20px;
    color: var(--text-color);
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: center;
}

.contact-details p {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 5px;
}

.contact-details i {
    color: var(--primary-color);
    width: 20px;
}

/* Contact and About Page Styles */
.contact-page header,
.about-page header {
    position: relative;
    background-color: var(--primary-color);
}

.contact-section {
    padding: 80px 0;
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
}

.contact-info {
    background-color: var(--light-bg);
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.contact-info h3 {
    margin-bottom: 20px;
    position: relative;
    font-size: 1.5rem;
}

.contact-info h3:after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background-color: var(--accent-color);
    margin-top: 10px;
}

.branch-details {
    margin-bottom: 30px;
}

.contact-info p {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.contact-info i {
    color: var(--primary-color);
    font-size: 1.2rem;
    width: 25px;
}

.contact-form {
    background-color: var(--light-text);
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-family: var(--font-primary);
    font-size: 1rem;
}

.form-group textarea {
    height: 150px;
    resize: vertical;
}

.map-section {
    padding: 0 0 80px;
}

.map-container {
    height: 450px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.map-container iframe {
    width: 100%;
    height: 100%;
    border: 0;
}

.business-hours {
    margin-top: 30px;
}

.business-hours ul {
    list-style: none;
}

.business-hours li {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px dashed var(--border-color);
}

@media (max-width: 992px) {
    .contact-grid {
        grid-template-columns: 1fr;
    }
}

/* Image Lightbox Styles */
.lightbox-modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(5px);
}

.lightbox-content {
    position: relative;
    margin: auto;
    padding: 20px;
    width: 90%;
    max-width: 1200px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lightbox-content img {
    max-width: 100%;
    max-height: 90vh;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.lightbox-close {
    position: absolute;
    top: 20px;
    right: 30px;
    color: var(--light-text);
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
    z-index: 10001;
    transition: var(--transition);
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
}

.lightbox-close:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
}

/* Make product images clickable */
.product-image img {
    cursor: pointer;
    transition: var(--transition);
}

.product-image img:hover {
    transform: scale(1.05);
    opacity: 0.9;
}

/* No Results Message */
.no-results-message {
    text-align: center;
    padding: 40px 20px;
    background-color: var(--light-bg);
    border-radius: 8px;
    margin: 20px 0;
}

.no-results-message p {
    color: var(--text-color);
    font-size: 1.1rem;
    margin: 0;
}