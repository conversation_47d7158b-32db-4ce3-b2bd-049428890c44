// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Featured Promo Slideshow functionality
    const promoSlides = document.querySelectorAll('.featured-promo .slide');
    const promoDots = document.querySelectorAll('.featured-promo .slide-dot');
    let currentPromoSlide = 0;
    let promoSlideshowInterval;

    console.log('Promo slides found:', promoSlides.length);
    console.log('Promo dots found:', promoDots.length);

    // Initialize promo slideshow
    function startPromoSlideshow() {
        if (promoSlides.length > 1) {
            promoSlideshowInterval = setInterval(nextPromoSlide, 4000); // Change slide every 4 seconds
        }
    }

    // Move to next promo slide
    function nextPromoSlide() {
        goToPromoSlide((currentPromoSlide + 1) % promoSlides.length);
    }

    // Go to specific promo slide
    function goToPromoSlide(slideIndex) {
        if (promoSlides.length === 0) return;

        // Remove active class from current slide and dot
        promoSlides[currentPromoSlide].classList.remove('active');
        if (promoDots[currentPromoSlide]) {
            promoDots[currentPromoSlide].classList.remove('active');
        }

        // Update current slide index
        currentPromoSlide = slideIndex;

        // Add active class to new slide and dot
        promoSlides[currentPromoSlide].classList.add('active');
        if (promoDots[currentPromoSlide]) {
            promoDots[currentPromoSlide].classList.add('active');
        }
    }

    // Add click event to promo dots
    promoDots.forEach(dot => {
        dot.addEventListener('click', function() {
            // Clear the interval when manually changing slides
            clearInterval(promoSlideshowInterval);

            // Get slide index from data attribute
            const slideIndex = parseInt(this.getAttribute('data-slide'));
            goToPromoSlide(slideIndex);

            // Restart slideshow after manual navigation
            startPromoSlideshow();
        });
    });

    // Add click events for featured promo navigation buttons
    const prevSlideButton = document.querySelector('.featured-promo .prev-slide');
    const nextSlideButton = document.querySelector('.featured-promo .next-slide');

    console.log('Previous button found:', prevSlideButton);
    console.log('Next button found:', nextSlideButton);

    if (prevSlideButton) {
        prevSlideButton.addEventListener('click', function() {
            console.log('Previous button clicked');
            // Clear the interval when manually changing slides
            clearInterval(promoSlideshowInterval);

            // Calculate previous slide index (with wrap-around)
            const prevIndex = (currentPromoSlide - 1 + promoSlides.length) % promoSlides.length;
            console.log('Going to slide:', prevIndex);
            goToPromoSlide(prevIndex);

            // Restart slideshow after manual navigation
            startPromoSlideshow();
        });
    } else {
        console.log('Previous button not found');
    }

    if (nextSlideButton) {
        nextSlideButton.addEventListener('click', function() {
            console.log('Next button clicked');
            // Clear the interval when manually changing slides
            clearInterval(promoSlideshowInterval);

            // Go to next slide
            nextPromoSlide();

            // Restart slideshow after manual navigation
            startPromoSlideshow();
        });
    } else {
        console.log('Next button not found');
    }

    // Start the promo slideshow
    if (promoSlides.length > 0) {
        startPromoSlideshow();
    }
});

// Testimonial Slideshow functionality
const testimonialSlides = document.querySelectorAll('.testimonial-slide');
const testimonialDots = document.querySelectorAll('.testimonial-dot');
let currentTestimonial = 0;
let testimonialInterval;

// Initialize testimonial slideshow
function startTestimonialSlideshow() {
    testimonialInterval = setInterval(nextTestimonial, 5000); // Change testimonial every 5 seconds
}

// Move to next testimonial
function nextTestimonial() {
    goToTestimonial((currentTestimonial + 1) % testimonialSlides.length);
}

// Go to specific testimonial
function goToTestimonial(testimonialIndex) {
    // Remove active class from current testimonial and dot
    testimonialSlides[currentTestimonial].classList.remove('active');
    testimonialDots[currentTestimonial].classList.remove('active');

    // Update current testimonial index
    currentTestimonial = testimonialIndex;

    // Add active class to new testimonial and dot
    testimonialSlides[currentTestimonial].classList.add('active');
    testimonialDots[currentTestimonial].classList.add('active');
}

// Add click event to testimonial dots
testimonialDots.forEach(dot => {
    dot.addEventListener('click', function() {
        // Clear the interval when manually changing testimonials
        clearInterval(testimonialInterval);

        // Get testimonial index from data attribute
        const testimonialIndex = parseInt(this.getAttribute('data-slide'));
        goToTestimonial(testimonialIndex);

        // Restart testimonial slideshow after manual navigation
        startTestimonialSlideshow();
    });
});

// Add click events for previous and next buttons
const prevButton = document.querySelector('.prev-testimonial');
const nextButton = document.querySelector('.next-testimonial');

if (prevButton) {
    prevButton.addEventListener('click', function() {
        // Clear the interval when manually changing testimonials
        clearInterval(testimonialInterval);

        // Calculate previous testimonial index (with wrap-around)
        const prevIndex = (currentTestimonial - 1 + testimonialSlides.length) % testimonialSlides.length;
        goToTestimonial(prevIndex);

        // Restart testimonial slideshow after manual navigation
        startTestimonialSlideshow();
    });
}

if (nextButton) {
    nextButton.addEventListener('click', function() {
        // Clear the interval when manually changing testimonials
        clearInterval(testimonialInterval);

        // Go to next testimonial
        nextTestimonial();

        // Restart testimonial slideshow after manual navigation
        startTestimonialSlideshow();
    });
}

// Start the testimonial slideshow
if (testimonialSlides.length > 0) {
    startTestimonialSlideshow();
}
// Ensure all DOM content is loaded before running scripts
document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu functionality
    const mobileMenuBtn = document.querySelector('.mobile-menu');
    const nav = document.querySelector('nav');
    const navLinks = document.querySelector('.nav-links');

    if (mobileMenuBtn) {
        // Initialize nav position to ensure it's set
        if (!nav.style.left) {
            nav.style.left = '-100%';
        }

        mobileMenuBtn.addEventListener('click', function(e) {
            e.stopPropagation(); // Prevent event from bubbling up
            this.classList.toggle('active');
            nav.style.left = nav.style.left === '0px' ? '-100%' : '0px';
            console.log('Mobile menu clicked, nav left:', nav.style.left);
        });

        // Close menu when clicking a link
        if (navLinks) {
            navLinks.querySelectorAll('a').forEach(link => {
                link.addEventListener('click', () => {
                    nav.style.left = '-100%';
                    mobileMenuBtn.classList.remove('active');
                    console.log('Link clicked, nav left:', nav.style.left);
                });
            });
        }

        // Close menu when clicking outside
        document.addEventListener('click', (e) => {
            if (nav && !nav.contains(e.target) && !mobileMenuBtn.contains(e.target) && nav.style.left === '0px') {
                nav.style.left = '-100%';
                mobileMenuBtn.classList.remove('active');
                console.log('Outside clicked, nav left:', nav.style.left);
            }
        });
    }

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href');
            if (targetId === '#') return;

            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                window.scrollTo({
                    top: targetElement.offsetTop - 100,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Testimonial Slider
    const testimonialDots = document.querySelectorAll('.testimonial-dots .dot');
    const testimonials = document.querySelectorAll('.testimonial');
    let currentTestimonial = 0;

    // Initialize testimonials display
    if (testimonials.length > 0) {
        testimonials.forEach((testimonial, index) => {
            if (index !== 0) {
                testimonial.style.display = 'none';
            }
        });
    }

    // Handle dot clicks
    if (testimonialDots.length > 0) {
        testimonialDots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                showTestimonial(index);
            });
        });
    }

    // Auto rotate testimonials
    if (testimonials.length > 1) {
        setInterval(() => {
            currentTestimonial = (currentTestimonial + 1) % testimonials.length;
            showTestimonial(currentTestimonial);
        }, 5000);
    }

    function showTestimonial(index) {
        testimonials.forEach((testimonial, i) => {
            testimonial.style.display = i === index ? 'block' : 'none';
        });

        testimonialDots.forEach((dot, i) => {
            dot.classList.toggle('active', i === index);
        });

        currentTestimonial = index;
    }


    // Newsletter form submission is now handled by Web3Forms in individual HTML pages

    // Notification function
    function showNotification(message) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = 'notification';
        notification.textContent = message;

        // Add to DOM
        document.body.appendChild(notification);

        // Show with animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // Remove after delay
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // Add CSS for notifications
    const notificationStyle = document.createElement('style');
    notificationStyle.textContent = `
        .notification {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: var(--primary-color);
            color: white;
            padding: 15px 25px;
            border-radius: 4px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transform: translateY(100px);
            opacity: 0;
            transition: transform 0.3s, opacity 0.3s;
            z-index: 1000;
        }

        .notification.show {
            transform: translateY(0);
            opacity: 1;
        }

        @media (max-width: 768px) {
            .nav-links.active, .nav-icons.active {
                display: flex;
                flex-direction: column;
                position: absolute;
                top: 100%;
                left: 0;
                width: 100%;
                background-color: var(--primary-color);
                padding: 20px;
                box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
                z-index: 100;
            }

            .nav-links.active {
                align-items: center;
            }

            .nav-icons.active {
                flex-direction: row;
                justify-content: center;
                gap: 30px;
                padding-top: 0;
            }
        }
    `;
    document.head.appendChild(notificationStyle);

    // Back to Top Button Functionality
    const createBackToTopButton = () => {
        // Create the button element
        const backToTopButton = document.createElement('div');
        backToTopButton.className = 'back-to-top';
        backToTopButton.innerHTML = '<i class="fas fa-arrow-up"></i>';

        // Append to body
        document.body.appendChild(backToTopButton);

        // Show/hide button based on scroll position
        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopButton.classList.add('active');
            } else {
                backToTopButton.classList.remove('active');
            }
        });

        // Scroll to top when clicked
        backToTopButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    };

    // Create the back to top button
    createBackToTopButton();

    // Catalogue Page Functionality - Updated to work with sidebar filtering
    const setupCatalogueFilters = () => {
        console.log('Setting up catalogue filters...');

        // Function to filter products by category
        window.filterProductsByCategory = (selectedCategory) => {
            console.log('Filtering by category:', selectedCategory);

            // Get all product cards
            const productCards = document.querySelectorAll('.product-card');
            let visibleCount = 0;

            productCards.forEach(card => {
                const cardCategory = card.getAttribute('data-category');

                if (selectedCategory === 'all' || cardCategory === selectedCategory) {
                    card.style.display = 'block';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            });

            // Show/hide no results message
            const noResultsMessage = document.getElementById('no-results-message');
            if (noResultsMessage) {
                if (visibleCount === 0 && selectedCategory !== 'all') {
                    noResultsMessage.style.display = 'block';
                } else {
                    noResultsMessage.style.display = 'none';
                }
            }

            console.log(`Filtered results: ${visibleCount} products visible`);
        };

        // Initialize with all products visible
        window.filterProductsByCategory('all');
    };

    // Initialize catalogue filters if on catalogue page
    if (document.querySelector('.catalogue-page')) {
        try {
            console.log('Catalogue page detected, setting up filters...');
            setupCatalogueFilters();

            // Handle URL fragment for category filtering
            const handleUrlFragment = () => {
                const hash = window.location.hash.substring(1); // Remove the # symbol
                if (hash) {
                    console.log('URL fragment detected:', hash);

                    // Find the corresponding category link and activate it
                    const categoryLinks = document.querySelectorAll('.category-link');
                    categoryLinks.forEach(link => {
                        link.classList.remove('active');
                        if (link.getAttribute('data-category') === hash) {
                            link.classList.add('active');
                        }
                    });

                    // Filter products by the hash category
                    if (window.filterProductsByCategory) {
                        window.filterProductsByCategory(hash);
                    }
                } else {
                    // No hash, show all categories
                    window.filterProductsByCategory('all');
                }
            };

            // Handle initial page load with fragment
            handleUrlFragment();

            // Handle hash changes (if user navigates with browser back/forward)
            window.addEventListener('hashchange', handleUrlFragment);

            console.log('Catalogue filters setup completed');
        } catch (error) {
            console.error('Error setting up catalogue filters:', error);
        }
    }

    // Product Details Modal System
    const productData = {
        'luxury-sofa-set': {
            name: 'Luxury Sofa Set',
            category: 'Living Room',
            price: 'GH₵ 12,500',
            image: 'images/luxury-sofa-set.jpg',
            description: 'Premium 3-seater sofa with matching armchairs in elegant fabric upholstery.',
            features: [
                'Premium fabric upholstery',
                'Solid hardwood frame',
                'High-density foam cushions',
                'Available in multiple colors',
                'Easy to clean and maintain',
                '3-year warranty included'
            ],
            specifications: {
                'Dimensions': '220cm x 95cm x 85cm',
                'Material': 'Hardwood frame with fabric upholstery',
                'Weight': '55kg',
                'Seating Capacity': '3 people',
                'Color Options': 'Brown, Grey, Navy Blue, Beige'
            }
        },
        'royal-elegance-sofa-set': {
            name: 'Royal Elegance Sofa Set',
            category: 'Living Room',
            price: 'GH₵ 18,900',
            image: 'images/royal-elegance-sofa--set.webp',
            description: 'Luxurious 5-piece set with premium leather upholstery and solid wood frame.',
            features: [
                'Premium leather upholstery',
                'Solid mahogany wood frame',
                'Memory foam cushions',
                'Handcrafted details',
                'Stain-resistant coating',
                '5-year warranty'
            ],
            specifications: {
                'Dimensions': '250cm x 100cm x 90cm',
                'Material': 'Mahogany frame with leather upholstery',
                'Weight': '75kg',
                'Seating Capacity': '5 people',
                'Color Options': 'Black, Brown, Burgundy'
            }
        },
        'modern-coffee-table': {
            name: 'Modern Coffee Table',
            category: 'Living Room',
            price: 'GH₵ 3,200',
            image: 'images/coffee-table.png',
            description: 'Contemporary design with tempered glass top and solid wood base.',
            features: [
                'Tempered glass top',
                'Solid wood base',
                'Modern minimalist design',
                'Easy to clean',
                'Scratch-resistant surface',
                '2-year warranty'
            ],
            specifications: {
                'Dimensions': '120cm x 60cm x 45cm',
                'Material': 'Tempered glass and solid wood',
                'Weight': '25kg',
                'Load Capacity': '50kg',
                'Color Options': 'Natural Wood, Dark Walnut'
            }
        },
        'king-size-bed-frame': {
            name: 'King Size Bed Frame',
            category: 'Bedroom',
            price: 'GH₵ 8,500',
            image: 'images/king-size-bed.jpg',
            description: 'Luxurious king-size bed with upholstered headboard and storage drawers.',
            features: [
                'Upholstered headboard',
                'Built-in storage drawers',
                'Solid wood construction',
                'Premium fabric finish',
                'Easy assembly',
                '3-year warranty'
            ],
            specifications: {
                'Dimensions': '200cm x 180cm x 120cm',
                'Material': 'Solid wood with fabric upholstery',
                'Weight': '65kg',
                'Storage': '4 under-bed drawers',
                'Color Options': 'Grey, Beige, Navy Blue'
            }
        },
        'modern-dining-set': {
            name: 'Modern Dining Set',
            category: 'Dining',
            price: 'GH₵ 9,800',
            image: 'images/modern-dining-set.jpg',
            description: 'Contemporary 6-seater dining set with tempered glass top and premium chairs.',
            features: [
                'Tempered glass table top',
                'Chrome steel legs',
                'Ergonomic chair design',
                'Easy to clean surface',
                'Modern contemporary style',
                '2-year warranty'
            ],
            specifications: {
                'Table Dimensions': '180cm x 90cm x 75cm',
                'Chair Dimensions': '45cm x 50cm x 95cm',
                'Material': 'Tempered glass, chrome steel, PU leather',
                'Seating Capacity': '6 people',
                'Color Options': 'Black/Chrome, White/Chrome'
            }
        },
        'executive-office-desk': {
            name: 'Executive Office Desk',
            category: 'Office',
            price: 'GH₵ 7,200',
            image: 'images/office-desk.jpg',
            description: 'Premium office desk with ample storage and elegant design.',
            features: [
                'Spacious work surface',
                'Multiple storage compartments',
                'Cable management system',
                'Scratch-resistant finish',
                'Modern professional design',
                '3-year warranty'
            ],
            specifications: {
                'Dimensions': '160cm x 80cm x 75cm',
                'Material': 'Engineered wood with laminate finish',
                'Weight': '45kg',
                'Storage': '3 drawers, 2 cabinets',
                'Color Options': 'Walnut, Oak, White'
            }
        },
        'executive-office-chair': {
            name: 'Executive Office Chair',
            category: 'Office',
            price: 'GH₵ 4,500',
            image: 'images/office-chair.jpg',
            description: 'Ergonomic design with premium leather and adjustable features.',
            features: [
                'Premium leather upholstery',
                'Ergonomic lumbar support',
                'Height adjustable',
                'Swivel and tilt mechanism',
                '360-degree rotation',
                '2-year warranty'
            ],
            specifications: {
                'Dimensions': '65cm x 70cm x 110-120cm',
                'Material': 'Genuine leather and steel frame',
                'Weight': '18kg',
                'Weight Capacity': '120kg',
                'Color Options': 'Black, Brown, Burgundy'
            }
        }
    };

    // Create and setup product details modal
    function createProductModal() {
        if (document.getElementById('product-modal')) return;

        const modalHTML = `
            <div id="product-modal" class="product-modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 id="modal-product-name"></h2>
                        <span class="modal-close">&times;</span>
                    </div>
                    <div class="modal-body">
                        <div class="modal-image">
                            <img id="modal-product-image" src="" alt="">
                        </div>
                        <div class="modal-details">
                            <div class="product-category">
                                <span id="modal-product-category"></span>
                            </div>
                            <div class="product-price">
                                <span id="modal-product-price"></span>
                            </div>
                            <div class="product-description">
                                <p id="modal-product-description"></p>
                            </div>
                            <div class="product-features">
                                <h3>Key Features</h3>
                                <ul id="modal-product-features"></ul>
                            </div>
                            <div class="product-specifications">
                                <h3>Specifications</h3>
                                <div id="modal-product-specs"></div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <a href="contact.html" class="btn-primary">Contact Us</a>
                        <a href="catalogue.html" class="btn-secondary">View More Products</a>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        setupModalEvents();
    }

    // Setup modal event handlers
    function setupModalEvents() {
        const modal = document.getElementById('product-modal');
        const closeBtn = document.querySelector('.modal-close');

        // Close modal when clicking X
        closeBtn.addEventListener('click', () => {
            modal.style.display = 'none';
        });

        // Close modal when clicking outside
        window.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && modal.style.display === 'block') {
                modal.style.display = 'none';
            }
        });
    }

    // Show product details in modal
    function showProductDetails(productKey) {
        const product = productData[productKey];
        if (!product) {
            console.error('Product not found:', productKey);
            return;
        }

        // Populate modal with product data
        document.getElementById('modal-product-name').textContent = product.name;
        document.getElementById('modal-product-category').textContent = product.category;
        document.getElementById('modal-product-price').textContent = product.price;
        document.getElementById('modal-product-description').textContent = product.description;
        document.getElementById('modal-product-image').src = product.image;
        document.getElementById('modal-product-image').alt = product.name;

        // Populate features
        const featuresList = document.getElementById('modal-product-features');
        featuresList.innerHTML = '';
        product.features.forEach(feature => {
            const li = document.createElement('li');
            li.textContent = feature;
            featuresList.appendChild(li);
        });

        // Populate specifications
        const specsList = document.getElementById('modal-product-specs');
        specsList.innerHTML = '';
        Object.entries(product.specifications).forEach(([key, value]) => {
            const specItem = document.createElement('div');
            specItem.className = 'spec-item';
            specItem.innerHTML = `<strong>${key}:</strong> ${value}`;
            specsList.appendChild(specItem);
        });

        // Show modal
        document.getElementById('product-modal').style.display = 'block';
    }

    // Setup product detail links
    function setupProductLinks() {
        // Create modal first
        createProductModal();

        // Setup click handlers for all "View Details" buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('btn-tertiary') && e.target.textContent.trim() === 'View Details') {
                e.preventDefault();

                // Get product key from the product card
                const productCard = e.target.closest('.product-card');
                if (productCard) {
                    const productName = productCard.querySelector('h3').textContent.trim();
                    const productKey = getProductKey(productName);

                    if (productKey && productData[productKey]) {
                        showProductDetails(productKey);
                    } else {
                        // Fallback for products without detailed data
                        alert('Product details coming soon! Please contact us for more information.');
                    }
                }
            }
        });
    }

    // Convert product name to product key
    function getProductKey(productName) {
        const keyMap = {
            'Luxury Sofa Set': 'luxury-sofa-set',
            'Royal Elegance Sofa Set': 'royal-elegance-sofa-set',
            'Modern Coffee Table': 'modern-coffee-table',
            'King Size Bed Frame': 'king-size-bed-frame',
            'Modern Dining Set': 'modern-dining-set',
            'Executive Office Desk': 'executive-office-desk',
            'Executive Office Chair': 'executive-office-chair'
        };
        return keyMap[productName] || null;
    }

    // Initialize product details system
    setupProductLinks();

    // Catalogue Sidebar Functionality
    function setupCatalogueSidebar() {
        const categoryLinks = document.querySelectorAll('.category-link');
        const mobileSidebarBtn = document.getElementById('mobile-sidebar-btn');
        const sidebar = document.querySelector('.catalogue-sidebar');

        // Create overlay for mobile
        const overlay = document.createElement('div');
        overlay.className = 'sidebar-overlay';
        document.body.appendChild(overlay);

        // Category filtering
        categoryLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();

                // Remove active class from all links
                categoryLinks.forEach(l => l.classList.remove('active'));

                // Add active class to clicked link
                link.classList.add('active');

                // Get category
                const category = link.getAttribute('data-category');

                // Update URL hash
                if (category === 'all') {
                    // Remove hash for "all categories"
                    history.pushState(null, null, window.location.pathname);
                } else {
                    // Set hash for specific category
                    window.location.hash = category;
                }

                // Use the new filtering function
                if (window.filterProductsByCategory) {
                    window.filterProductsByCategory(category);
                }

                // Close mobile sidebar if open
                if (window.innerWidth <= 768) {
                    sidebar.classList.remove('active');
                    overlay.classList.remove('active');
                }

                // Scroll to top - if footer link, scroll to very top, otherwise scroll to main content
                if (link.closest('footer')) {
                    // Footer category link - scroll to top of page
                    window.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                    });
                } else {
                    // Sidebar category link - scroll to main content
                    document.querySelector('.catalogue-main').scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Mobile sidebar toggle
        if (mobileSidebarBtn) {
            mobileSidebarBtn.addEventListener('click', () => {
                sidebar.classList.toggle('active');
                overlay.classList.toggle('active');
            });
        }

        // Close sidebar when clicking overlay
        overlay.addEventListener('click', () => {
            sidebar.classList.remove('active');
            overlay.classList.remove('active');
        });

        // Close sidebar on window resize if mobile view is exited
        window.addEventListener('resize', () => {
            if (window.innerWidth > 768) {
                sidebar.classList.remove('active');
                overlay.classList.remove('active');
            }
        });
    }

    // Image Lightbox Functionality
    function setupImageLightbox() {
        const lightbox = document.getElementById('image-lightbox');
        const lightboxImage = document.getElementById('lightbox-image');
        const lightboxClose = document.querySelector('.lightbox-close');

        if (!lightbox || !lightboxImage || !lightboxClose) return;

        // Add click event to all product images
        document.addEventListener('click', (e) => {
            if (e.target.matches('.product-image img')) {
                e.preventDefault();
                lightboxImage.src = e.target.src;
                lightboxImage.alt = e.target.alt || 'Product Image';
                lightbox.style.display = 'block';
                document.body.style.overflow = 'hidden'; // Prevent background scrolling
            }
        });

        // Close lightbox when clicking the close button
        lightboxClose.addEventListener('click', () => {
            lightbox.style.display = 'none';
            document.body.style.overflow = 'auto';
        });

        // Close lightbox when clicking outside the image
        lightbox.addEventListener('click', (e) => {
            if (e.target === lightbox) {
                lightbox.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        });

        // Close lightbox with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && lightbox.style.display === 'block') {
                lightbox.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        });
    }

    // Initialize catalogue sidebar if on catalogue page
    if (document.querySelector('.catalogue-page')) {
        setupCatalogueSidebar();
        setupImageLightbox();
    }
});
