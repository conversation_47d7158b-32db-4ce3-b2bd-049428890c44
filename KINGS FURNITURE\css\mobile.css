/* Mobile Styles for Kings Furniture */

/* Mobile Menu Styles */
@media (max-width: 768px) {
    nav {
        position: fixed;
        top: 0;
        left: -100%;
        width: 80%;
        height: 100vh;
        background-color: var(--primary-color);
        padding: 80px 20px 20px;
        transition: left 0.3s ease;
        z-index: 999;
        overflow-y: auto;
    }

    nav .nav-links {
        display: flex !important;
        flex-direction: column;
        align-items: flex-start;
        gap: 20px;
    }
}

.mobile-menu {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--light-text);
    z-index: 1001;
}

.mobile-menu.active i {
    transform: rotate(90deg);
}

.mobile-menu i {
    transition: transform 0.3s ease;
}

/* Mobile Specific Styles */
@media (max-width: 768px) {

    /* Global Styles */
    body {
        min-width: unset;
        overflow-x: hidden;
    }

    .container {
        width: 100%;
        max-width: 1200px;
        padding: 0 15px 0 15px;
    }

    /* Header and Navigation */
    header {
        position: relative;
        background-color: var(--primary-color);
    }

    header .container {
        padding: 15px 10px 15px 10px;
        justify-content: space-between;
    }

    header.home-header {
        position: absolute;
        background-color: transparent;
    }

    .about-page header,
    .contact-page header {
        position: relative;
        background-color: var(--primary-color);
    }

    .mobile-menu {
        display: block;
        margin-right: 15px;
    }

    .nav-links a {
        display: block;
        padding: 10px 0;
        font-size: 1.1rem;
        color: var(--light-text);
    }

    /* Override the display: none from style.css */
    header nav .nav-links {
        display: flex !important;
    }

    .logo-container {
        flex: 0 0 180px;
        justify-content: flex-start;
        padding-left: 10px;
    }

    .logo {
        max-height: 75px;
    }



    /* Featured Promo Mobile */
    .featured-promo {
        height: 40vh;
        margin: 10px 0;
    }

    .featured-promo .slideshow-nav {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .featured-promo .prev-slide {
        left: 15px;
    }

    .featured-promo .next-slide {
        right: 15px;
    }

    .featured-promo .slideshow-controls {
        bottom: 15px;
        gap: 8px;
    }

    .featured-promo .slide-dot {
        width: 10px;
        height: 10px;
    }

    /* Collection Grid */
    .collection-grid {
        grid-template-columns: 1fr;
        grid-template-rows: auto;
    }

    .collection-card {
        height: 220px;
    }

    /* Product Grid */
    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }

    /* Catalogue Page Mobile Styles */
    .page-banner {
        height: 200px;
    }

    .page-banner .banner-content {
        width: 100%;
        padding: 0 15px;
    }

    .page-banner h1 {
        font-size: 2.2rem;
        width: 100%;
        text-align: center;
    }

    .page-banner p {
        font-size: 0.9rem;
        width: 100%;
        text-align: center;
    }

    .filter-container {
        flex-direction: column;
        align-items: stretch;
        padding: 15px;
    }

    .filter-group {
        flex-direction: column;
        align-items: flex-start;
    }

    .filter-select {
        width: 100%;
    }

    .catalogue-section {
        padding: 30px 0;
    }

    .catalogue-section h2 {
        font-size: 1.8rem;
    }

    /* Testimonials */
    .testimonial-nav {
        width: 35px;
        height: 35px;
    }

    .testimonial-nav i {
        font-size: 1rem;
    }

    .testimonial-content {
        margin: 0 40px;
        padding: 30px;
    }

    /* About Page Styles */
    .page-header {
        padding: 40px 0;
        text-align: center;
    }

    .page-header h1 {
        font-size: 2rem;
        margin-bottom: 10px;
    }

    .breadcrumb {
        font-size: 0.9rem;
    }

    .story-content {
        display: flex;
        flex-direction: column;
        gap: 30px;
    }

    .story-image {
        order: -1;
        margin-bottom: 20px;
        text-align: center;
    }

    .story-image img {
        max-width: 100%;
        height: auto;
    }

    .mission-vision-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .values-grid {
        grid-template-columns: 1fr !important;
        gap: 20px;
    }

    .brands-grid {
        grid-template-columns: 1fr !important;
        gap: 20px;
    }

    .brand-card {
        padding: 20px 15px;
    }

    .brand-logo {
        height: 70px;
        margin-bottom: 15px;
    }

    /* Privacy Policy Page Mobile */
    .privacy-content {
        padding: 60px 0;
    }

    .privacy-policy {
        padding: 30px 20px;
        margin: 0 15px;
    }

    .policy-header h2 {
        font-size: 1.8rem;
    }

    .policy-header h3 {
        font-size: 1.3rem;
    }

    .policy-section h3 {
        font-size: 1.2rem;
    }

    .contact-details {
        padding: 15px;
    }

    /* Customer Care Pages Mobile */
    .faq-content,
    .delivery-content,
    .warranty-content,
    .terms-content {
        padding: 60px 0;
    }

    .faq-sections,
    .delivery-info,
    .warranty-info,
    .terms-document {
        margin: 0 15px;
    }

    .faq-section,
    .info-section,
    .warranty-section {
        padding: 25px 20px;
        margin-bottom: 30px;
    }

    .faq-section h2,
    .info-section h2,
    .warranty-section h2 {
        font-size: 1.6rem;
    }

    .faq-item h3,
    .info-item h3,
    .warranty-item h3 {
        font-size: 1.1rem;
    }

    .terms-document {
        padding: 30px 20px;
    }

    .terms-section h2 {
        font-size: 1.2rem;
    }

    .claim-steps {
        gap: 15px;
    }

    .step {
        flex-direction: column;
        text-align: center;
        padding: 20px 15px;
    }

    .step-number {
        margin-bottom: 15px;
    }

    .contact-section {
        padding: 25px 20px;
        margin-top: 25px;
    }

    .contact-details {
        align-items: flex-start;
    }

    /* Contact Page - Show form first in mobile view */
    .contact-grid {
        display: grid;
        grid-template-columns: 1fr;
    }

    .contact-grid .contact-form {
        order: -1;
        /* This makes the form appear first */
        margin-bottom: 30px;
        padding: 25px 20px;
    }

    .contact-grid .contact-info {
        padding: 25px 20px;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-group label {
        margin-bottom: 5px;
    }

    .form-group input,
    .form-group textarea,
    .form-group select {
        padding: 10px;
    }

    .form-group textarea {
        height: 120px;
    }

    /* Footer */
    .footer-content {
        width: 100%;
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-logo {
        grid-column: span 2;
    }

    .newsletter-form {
        width: 100%;
        flex-direction: column;
    }

    .newsletter-form input {
        width: 100%;
        margin-bottom: 10px;
    }

    .newsletter-form button {
        width: 100%;
    }
}

@media (max-width: 576px) {

    /* Header */
    .logo {
        max-height: 65px;
    }



    /* Featured Promo Small Mobile */
    .featured-promo {
        height: 35vh;
        margin: 10px 0;
    }

    .featured-promo .slideshow-nav {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    .featured-promo .prev-slide {
        left: 10px;
    }

    .featured-promo .next-slide {
        right: 10px;
    }

    .featured-promo .slideshow-controls {
        bottom: 10px;
        gap: 6px;
    }

    .featured-promo .slide-dot {
        width: 8px;
        height: 8px;
    }

    /* Product Grid */
    .product-grid {
        grid-template-columns: 1fr;
    }

    /* Testimonials */
    .testimonial-nav {
        width: 30px;
        height: 30px;
    }

    .testimonial-content {
        margin: 0 35px;
        padding: 20px;
    }

    .testimonial-text {
        font-size: 0.9rem;
    }

    /* About Page Styles */
    .founding-story,
    .who-we-are,
    .mission-vision,
    .core-values,
    .our-brands,
    .corporate-responsibility,
    .sustainability,
    .recognition {
        padding: 40px 0;
    }

    .story-content {
        gap: 20px;
    }

    .experience-badge {
        transform: scale(0.8);
        right: 0;
        bottom: 0;
    }

    .core-values .value-card {
        padding: 25px 15px;
    }

    .core-values .value-card .icon {
        width: 60px;
        height: 60px;
    }

    .core-values .value-card .icon i {
        font-size: 1.5rem;
    }

    .core-values .value-card h3 {
        font-size: 1.2rem;
    }

    .core-values .value-card p {
        font-size: 0.9rem;
    }

    /* Privacy Policy Page Small Mobile */
    .privacy-content {
        padding: 40px 0;
    }

    .privacy-policy {
        padding: 25px 15px;
        margin: 0 10px;
    }

    .policy-header {
        margin-bottom: 30px;
        padding-bottom: 20px;
    }

    .policy-header h2 {
        font-size: 1.6rem;
    }

    .policy-header h3 {
        font-size: 1.2rem;
    }

    .policy-section {
        margin-bottom: 25px;
    }

    .policy-section h3 {
        font-size: 1.1rem;
    }

    .policy-footer {
        margin-top: 30px;
        padding-top: 20px;
    }

    /* Customer Care Pages Small Mobile */
    .faq-content,
    .delivery-content,
    .warranty-content,
    .terms-content {
        padding: 40px 0;
    }

    .faq-sections,
    .delivery-info,
    .warranty-info,
    .terms-document {
        margin: 0 10px;
    }

    .faq-section,
    .info-section,
    .warranty-section {
        padding: 20px 15px;
        margin-bottom: 25px;
    }

    .faq-section h2,
    .info-section h2,
    .warranty-section h2 {
        font-size: 1.4rem;
    }

    .faq-item h3,
    .info-item h3,
    .warranty-item h3 {
        font-size: 1rem;
    }

    .terms-document {
        padding: 25px 15px;
    }

    .terms-section h2 {
        font-size: 1.1rem;
    }

    .terms-section {
        margin-bottom: 25px;
    }

    .step {
        padding: 15px;
    }

    .contact-section {
        padding: 20px 15px;
        margin-top: 20px;
    }

    /* Contact Page - Ensure form is first on very small screens */
    .contact-grid .contact-form {
        margin-bottom: 25px;
        padding: 20px 15px;
    }

    .contact-grid .contact-info {
        padding: 20px 15px;
    }

    .contact-section {
        padding: 60px 0;
    }

    .map-section {
        padding: 0 0 60px;
    }

    .map-container {
        height: 350px;
    }

    /* Footer */
    .footer-content {
        grid-template-columns: 1fr;
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }

    /* News Page Mobile Styles */
    .news-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .news-card {
        margin: 0 10px;
    }

    .news-content {
        padding: 20px;
    }

    .news-content h3 {
        font-size: 1.2rem;
    }

    /* Product Modal Mobile Styles */
    .modal-content {
        width: 95%;
        margin: 5% auto;
    }

    .modal-header {
        padding: 20px;
    }

    .modal-header h2 {
        font-size: 1.5rem;
    }

    .modal-body {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 20px;
    }

    .modal-image img {
        height: 250px;
    }

    .product-price {
        font-size: 1.5rem;
    }

    .modal-actions {
        flex-direction: column;
        gap: 10px;
    }

    .modal-actions .btn-primary,
    .modal-actions .btn-secondary {
        width: 100%;
    }

    /* Article Page Mobile Styles */
    .article-content {
        padding: 20px;
        margin: 0 10px;
    }

    .article-meta {
        flex-direction: column;
        gap: 10px;
    }

    .article-image img {
        height: 250px;
    }

    .article-text h2 {
        font-size: 1.5rem;
    }

    .article-text h3 {
        font-size: 1.2rem;
    }

    .article-navigation {
        flex-direction: column;
        gap: 20px;
        align-items: flex-start;
    }

    .share-buttons {
        flex-wrap: wrap;
        gap: 10px;
    }

    .share-buttons span {
        width: 100%;
        margin-bottom: 10px;
    }

    /* Back to Top Button - Mobile */
    .back-to-top {
        bottom: 20px;
        right: 20px;
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    /* Catalogue Sidebar Mobile Styles */
    .catalogue-layout {
        flex-direction: column;
        padding: 0 15px;
    }

    .catalogue-sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: 80%;
        height: 100vh;
        z-index: 1001;
        transition: left 0.3s ease;
        overflow-y: auto;
        padding-top: 80px;
    }

    .catalogue-sidebar.active {
        left: 0;
    }

    .mobile-sidebar-toggle {
        display: block !important;
        margin-bottom: 20px;
    }

    .catalogue-main {
        width: 100%;
    }

    /* WhatsApp Button Mobile */
    .whatsapp-btn {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
        bottom: 20px;
        left: 20px;
    }

    /* Sidebar Overlay */
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        display: none;
    }

    .sidebar-overlay.active {
        display: block;
    }
}